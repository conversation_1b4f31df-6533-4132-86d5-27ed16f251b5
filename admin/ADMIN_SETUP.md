# Pet Patch Admin Panel Setup Guide

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd admin
npm install
```

### 2. Start Development Server

```bash
npm run dev
```

The admin panel will be available at `http://localhost:5173`

### 3. Default Login Credentials

- **Email**: `<EMAIL>`
- **Password**: `password`

## 📋 Features Implemented

### ✅ **Complete Admin Dashboard**

#### **🔐 Authentication**
- Secure admin login with JWT tokens
- Protected routes (admin-only access)
- Auto-redirect for unauthorized users

#### **📊 Dashboard Overview**
- System statistics (users, courses, revenue, role switching)
- Real-time analytics cards
- Recent activity feed
- Quick action buttons
- System health monitoring

#### **👥 User Management**
- Complete user listing with pagination
- Advanced filtering (user type, status, role switching)
- User search functionality
- Role switching management
- Bulk operations for role enabling
- User statistics and insights

#### **📚 Course Management**
- Full CRUD operations for courses
- Course filtering and search
- Course analytics and performance metrics
- Featured/popular course management
- Category-based organization
- Enrollment and completion tracking

#### **📈 Analytics Dashboard**
- System-wide performance metrics
- Course analytics (enrollments, completions, revenue)
- Role switching analytics
- Category performance breakdown
- Top performing courses
- Revenue tracking

#### **⚙️ Role Management**
- Enable/disable role switching for users
- Bulk role management operations
- Role switching analytics
- User role distribution
- Role switch history tracking

### 🎨 **UI/UX Features**

#### **Responsive Design**
- Mobile-first responsive layout
- Collapsible sidebar navigation
- Touch-friendly interface
- Adaptive grid layouts

#### **Modern Interface**
- Clean, professional design
- Consistent color scheme
- Loading states and animations
- Toast notifications
- Modal dialogs

#### **Navigation**
- Sidebar navigation with active states
- Breadcrumb navigation
- Quick access buttons
- Profile dropdown

## 🔧 Technical Implementation

### **Frontend Stack**
- **React 19** - Latest React version
- **Redux Toolkit** - State management
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Hot Toast** - Notifications
- **React Icons** - Icon library
- **Date-fns** - Date formatting

### **Project Structure**
```
admin/
├── src/
│   ├── components/          # Reusable components
│   │   ├── Layout/         # Main layout component
│   │   ├── StatsCard/      # Statistics cards
│   │   ├── UserTable/      # User management table
│   │   ├── CourseTable/    # Course management table
│   │   └── Modals/         # Modal components
│   ├── pages/              # Page components
│   │   ├── Dashboard/      # Dashboard page
│   │   ├── Users/          # User management
│   │   ├── Courses/        # Course management
│   │   ├── Analytics/      # Analytics dashboard
│   │   └── RoleManagement/ # Role switching management
│   ├── redux/              # State management
│   │   ├── store.js        # Redux store
│   │   └── slices/         # Redux slices
│   ├── services/           # API services
│   │   └── api.js          # Axios configuration
│   └── styles/             # CSS styles
└── package.json
```

### **State Management**
- **Auth Slice**: User authentication and role operations
- **Users Slice**: User management and filtering
- **Courses Slice**: Course CRUD operations
- **Analytics Slice**: Analytics data management
- **UI Slice**: UI state (modals, notifications, etc.)

## 🔌 API Integration

### **Backend Endpoints Used**

#### **Authentication**
- `POST /api/auth/login` - Admin login
- `POST /api/auth/enable-role-switching` - Enable role switching
- `POST /api/auth/admin/bulk-enable-roles` - Bulk enable roles
- `GET /api/auth/admin/role-analytics` - Role analytics

#### **Users**
- `GET /api/users` - Get all users with filtering
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

#### **Courses**
- `GET /api/courses/admin/all` - Get all courses
- `POST /api/courses/admin/create` - Create course
- `PUT /api/courses/admin/:id` - Update course
- `DELETE /api/courses/admin/:id` - Delete course
- `GET /api/courses/admin/analytics` - Course analytics

#### **Analytics**
- `GET /api/admin/system-stats` - System statistics
- `GET /api/courses/admin/analytics` - Course analytics
- `GET /api/auth/admin/role-analytics` - Role analytics

## 🎯 Key Features in Detail

### **Role Switching Management**
- **Individual Management**: Click on any user to manage their available roles
- **Bulk Operations**: Select multiple users and apply role changes
- **Analytics**: Track role switching usage and patterns
- **History**: View role switch history for each user

### **Course Management**
- **Create/Edit**: Full course creation and editing interface
- **Categories**: Organize courses by training categories
- **Difficulty Levels**: Set difficulty with visual indicators
- **Pricing**: Manage course pricing and discounts
- **Status**: Control course visibility and featured status

### **Analytics Dashboard**
- **Real-time Data**: Live statistics and metrics
- **Performance Tracking**: Monitor course and user engagement
- **Revenue Analytics**: Track course revenue and trends
- **User Insights**: Understand user behavior and preferences

## 🚀 Deployment

### **Build for Production**
```bash
npm run build
```

### **Preview Production Build**
```bash
npm run preview
```

## 🔒 Security Features

- **JWT Token Authentication**: Secure API communication
- **Admin-only Access**: Role-based access control
- **Protected Routes**: Automatic redirection for unauthorized access
- **Token Refresh**: Automatic token management
- **Input Validation**: Form validation and sanitization

## 📱 Responsive Design

- **Mobile-first**: Optimized for mobile devices
- **Tablet Support**: Adaptive layout for tablets
- **Desktop**: Full-featured desktop experience
- **Touch-friendly**: Large touch targets and gestures

## 🎨 Customization

### **Styling**
- CSS custom properties for easy theming
- Consistent design system
- Utility classes for rapid development
- Responsive breakpoints

### **Configuration**
- API base URL configuration
- Environment-specific settings
- Feature flags for different environments

## 🐛 Troubleshooting

### **Common Issues**

1. **Login Issues**
   - Ensure backend is running on port 5000
   - Check admin user exists in database
   - Verify JWT secret configuration

2. **API Connection**
   - Update API base URL in `src/services/api.js`
   - Check CORS configuration on backend
   - Verify network connectivity

3. **Build Issues**
   - Clear node_modules and reinstall
   - Check for dependency conflicts
   - Ensure all required dependencies are installed

## 📞 Support

For issues or questions:
1. Check the console for error messages
2. Verify backend API is running
3. Check network requests in browser dev tools
4. Ensure proper admin credentials

## 🎉 Ready to Use!

The admin panel is now fully functional with:
- ✅ Complete user management
- ✅ Role switching controls
- ✅ Course management system
- ✅ Analytics dashboard
- ✅ Responsive design
- ✅ Modern UI/UX

Start the development server and begin managing your Pet Patch platform! 🐾
