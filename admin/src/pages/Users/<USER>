/* Users Page - Tailwind to CSS migration */

.users-page {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.users-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.users-header-title {
  font-size: 2rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 0.25rem;
}

.users-header-desc {
  color: #6b7280;
}

.users-header-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.users-btn-primary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #2563eb;
  color: #fff;
  font-weight: 500;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.users-btn-primary:hover {
  background: #1d4ed8;
}

.users-btn-secondary {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  font-weight: 500;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.users-btn-secondary:hover {
  background: #e5e7eb;
}

.users-card {
  background: #fff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.users-card-body {
  padding: 0;
}

.users-search-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media (min-width: 640px) {
  .users-search-row {
    flex-direction: row;
  }
}

.users-search-input-container {
  position: relative;
  flex: 1;
}

.users-search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  color: #111827;
  background: #fff;
}

.users-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  width: 1.25rem;
  height: 1.25rem;
}

.users-filter-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.users-filter-btn:hover {
  background: #e5e7eb;
}

/* Add more as needed for table, filters, etc. */
