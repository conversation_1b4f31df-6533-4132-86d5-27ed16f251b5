class ProductRequest {
  final String name;
  final String description;
  final double price;
  final String manufacturer;
  final double shippingCost;
  final double? monthlyDeliveryPrice;
  final String brand;
  final String itemWeight;
  final String itemForm;
  final String ageRange;
  final String breedRecommendation;
  final String dietType;
  final List<String> images;
  final int stock;
  final bool subscriptionAvailable;
  final String category;

  ProductRequest({
    required this.name,
    required this.description,
    required this.price,
    required this.manufacturer,
    required this.shippingCost,
    this.monthlyDeliveryPrice,
    required this.brand,
    required this.itemWeight,
    required this.itemForm,
    required this.ageRange,
    required this.breedRecommendation,
    required this.dietType,
    required this.images,
    required this.stock,
    required this.subscriptionAvailable,
    required this.category,
  });

  Map<String, dynamic> toJson() => {
    "name": name,
    "description": description,
    "price": price,
    "manufacturer": manufacturer,
    "shippingCost": shippingCost,
    "monthlyDeliveryPrice": monthlyDeliveryPrice ?? 0,
    "brand": brand,
    "itemWeight": itemWeight,
    "itemForm": itemForm,
    "ageRange": ageRange,
    "breedRecommendation": breedRecommendation,
    "dietType": dietType,
    "images": images,
    "stock": stock,
    "subscriptionAvailable": subscriptionAvailable,
    "category": category,
  };
}
