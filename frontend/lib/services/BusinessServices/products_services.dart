import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../features/screen/business/model/addproductsmodel.dart';
import '../../utlis/app_config/app_config.dart';

class ProductApiService {
  static Future<bool> createProduct({
    required String token,
    required ProductRequest product,
  }) async {
    final url = Uri.parse('${AppConfig.baseUrl}/product/');

    final response = await http.post(
      url,
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(product.toJson()),
    );

    if (response.statusCode == 200) {
      print('✅ Product created: ${response.body}');
      return true;
    } else {
      print('❌ Error: ${response.statusCode}');
      print(response.body);
      return false;
    }
  }
}
