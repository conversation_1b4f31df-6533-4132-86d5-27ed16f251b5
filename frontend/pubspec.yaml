name: petcare
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  curved_labeled_navigation_bar: ^2.0.6
  intl: ^0.18.1
  image_picker: ^1.0.5
  dotted_border: ^2.0.0
  fl_chart: ^0.71.0
  iconsax_flutter: ^1.0.1

  #auth
  http: ^1.4.0
  provider: ^6.1.5
  shared_preferences: ^2.2.2
  url_launcher: ^6.2.6
  google_maps_flutter: ^2.12.3
  geolocator: ^11.0.0
  permission_handler: ^12.0.1
  geocoding: ^4.0.0
  mime: ^2.0.0
  http_parser: ^4.1.2
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/fonts/Encode_Sans_Expanded/
    - assets/fonts/Playfair_Display/
    - assets/auth_images/background_splash.jpg
    - assets/auth_images/logo.png
    - assets/auth_images/welcome.png
    - assets/auth_images/google.png
    - assets/auth_images/facebook.png
    - assets/homeimage/
    - assets/Dog/
    - assets/profile/
    - assets/products/
    - assets/store/
    - assets/person/
    - assets/cards/


  fonts:
    - family: Playfair Display
      fonts:
        - asset: assets/fonts/Playfair_Display/static/PlayfairDisplay-Regular.ttf
          weight: 400
        - asset: assets/fonts/Playfair_Display/static/PlayfairDisplay-Medium.ttf
          weight: 500
        - asset: assets/fonts/Playfair_Display/static/PlayfairDisplay-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Playfair_Display/static/PlayfairDisplay-Bold.ttf
          weight: 700
        - asset: assets/fonts/Playfair_Display/static/PlayfairDisplay-Italic.ttf
          style: italic

    - family: EncodeSansExpanded
      fonts:
        - asset: assets/fonts/Encode_Sans_Expanded/EncodeSansExpanded-Regular.ttf
          weight: 400
        - asset: assets/fonts/Encode_Sans_Expanded/EncodeSansExpanded-Medium.ttf
          weight: 500
        - asset: assets/fonts/Encode_Sans_Expanded/EncodeSansExpanded-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Encode_Sans_Expanded/EncodeSansExpanded-Bold.ttf
          weight: 700
